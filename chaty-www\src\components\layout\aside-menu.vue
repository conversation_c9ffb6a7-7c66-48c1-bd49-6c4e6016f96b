<template>
  <div class="menu-bar">
    <el-menu :default-active="activeIndex" class="el-menu-demo" mode="vertical" :ellipsis="false"
             router active-text-color="#0052CC">
      <el-menu-item v-for="item in menuItems" v-permission="item.authority" v-bind="item" :key="item.index">
        <component
          class="icons"
          :is="getSvgComponent(item.icon, activeIndex === item.index)"
          :class="{ 'active-icon': activeIndex === item.index }"
        />
        <template #title>{{ $t(item.t) }}</template>
      </el-menu-item>
    </el-menu>

    <el-tooltip placement="top" content="关闭侧边栏">
      <el-image src="/icon/collapse.png" class="collapse-icon" @click="changeIsCollapse"></el-image>
    </el-tooltip>
  </div>
</template>

<script>
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store";
import { defineAsyncComponent } from 'vue';
// 静态导入所有SVG
import Bell from '@/assets/svg/bell.svg';
import BellActive from '@/assets/svg/bell - 副本.svg';
import Tool from '@/assets/svg/tool.svg';
import ToolActive from '@/assets/svg/tool - 副本.svg';
import Dashboard from '@/assets/svg/dashboard.svg';
import DashboardActive from '@/assets/svg/dashboard - 副本.svg';
import Box from '@/assets/svg/box.svg';
import BoxActive from '@/assets/svg/box - 副本.svg';
import ChatRemove from '@/assets/svg/chat-remove.svg';
import ChatRemoveActive from '@/assets/svg/chat-remove - 副本.svg';
import Celluar from '@/assets/svg/celluar.svg';
import CelluarActive from '@/assets/svg/celluar - 副本.svg';
import Checkbox from '@/assets/svg/checkbox.svg';
import CheckboxActive from '@/assets/svg/checkbox - 副本.svg';
import Cursor from '@/assets/svg/cursor.svg';
import CursorActive from '@/assets/svg/cursor - 副本.svg';
import HomeAlt2 from '@/assets/svg/home-alt2.svg';
import HomeAlt2Active from '@/assets/svg/home-alt2 - 副本.svg';
import Next from '@/assets/svg/next.svg';
import NextActive from '@/assets/svg/next - 副本.svg';
import Alarm from '@/assets/svg/alarm.svg';
import AlarmActive from '@/assets/svg/alarm - 副本.svg';

const iconMap = {
  bell: Bell,
  'bell - 副本': BellActive,
  tool: Tool,
  'tool - 副本': ToolActive,
  dashboard: Dashboard,
  'dashboard - 副本': DashboardActive,
  box: Box,
  'box - 副本': BoxActive,
  'chat-remove': ChatRemove,
  'chat-remove - 副本': ChatRemoveActive,
  celluar: Celluar,
  'celluar - 副本': CelluarActive,
  checkbox: Checkbox,
  'checkbox - 副本': CheckboxActive,
  cursor: Cursor,
  'cursor - 副本': CursorActive,
  'home-alt2': HomeAlt2,
  'home-alt2 - 副本': HomeAlt2Active,
  next: Next,
  'next - 副本': NextActive,
  alarm: Alarm,
  'alarm - 副本': AlarmActive,
};

export default {
  props: {
    menuItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeIndex: '',
      svgCache: {},
    }
  },
  created() {
    this.setActiveIndex(this.$route.path)
  },
  computed: {
    ...mapState(useUserStore, ['getIsCollapse']),
  },
  watch: {
    $route(to) {
      this.setActiveIndex(to.path)
    }
  },
  methods: {
    ...mapActions(useUserStore, ['changeIsCollapse']),
    setActiveIndex(path) {
      const pathSegments = path.split('/'); // 根据 '/' 分隔路径
      if ((pathSegments[1] || '') === 'docconfig') {
        this.activeIndex = '/correctConfigPackages'
      } else if ((pathSegments[1] || '') === 'class') {
        this.activeIndex = '/class/config'
      } else if ((pathSegments[1] || '') === 'grading') {
        this.activeIndex = '/grading/correct'
      } else if ((pathSegments[1] || '') === 'survey') {
        this.activeIndex = '/survey/page'
      } else if ((pathSegments[1] || '') === 'errorCorrectionStatistics') {
        this.activeIndex = '/errorCorrectionStatistics/index'
      } else {
        this.activeIndex = '/' + (pathSegments[1] || '');
      }
    },
    getSvgComponent(icon, isActive) {
      if (!icon) return null;
      const suffix = isActive ? ' - 副本' : '';
      return iconMap[icon + suffix] || null;
    },
  }
}
</script>

<style lang="scss" scoped>
.menu-bar {
  display: flex;
  flex-direction: column;
  border-right: thin solid #dcdfe6;
  width: 180px;
  height: 100%;
  opacity: 0.82;
  padding: 12px 10px;
  z-index: 1;
  background-image: linear-gradient(180deg, #FFFFFF 0%, #ffffffd1 100%);

  .el-menu-item {
    width: 160px;
    height: 48px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 1.4;
    padding: 0 12px;
  }

  .el-menu-item:focus {
    font-weight: 500;
    font-size: 14px;
    color: #0052CC;
    background: #E9F2FF;
    border-radius: 6px;
  }

  .el-menu-item.is-active {
    font-weight: 600;
    font-size: 14px;
    color: #0052CC;
    background: #E9F2FF;
    border-radius: 6px;
  }

  .el-menu-item.is-active:hover {
    font-weight: 600;
    font-size: 14px;
    color: #0052CC;
    background: #E9F2FF;
    border-radius: 6px;
  }

  .el-menu-item:hover {
    font-weight: 500;
    font-size: 14px;
    color: #0052CC;
    background: #F4F8FF;
    border-radius: 6px;
  }

  .el-menu-demo {
    margin-top: 10px;
    border-right: none !important;
  }

  .flex-grow {
    flex-grow: 1;
  }

  .el-menu-demo {
    display: flex;
    flex-direction: column;
    border-right: none !important;
  }

  .menu-bottom {
    .icons {
      width: 15px;
      height: 15px;
    }
  }

  .icons {
    width: 18px;
    height: 18px;
    margin-right: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .active-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
  }

  .collapse-icon {
    width: 22px;
    height: 22px;
    bottom: 20px;
    left: 149px;
    position: fixed;
  }
}

</style>